# Rate Limiting Bypass Strategies

## Problem
When deployed on Render, the backend was receiving HTTP 418 errors from Binance API, indicating rate limiting or IP blocking.

## Solutions Implemented

### 1. Enhanced Axios Configuration
- **Custom User Agent**: Added realistic browser user agent to avoid bot detection
- **Proper Headers**: Added standard browser headers (Accept, Accept-Language, etc.)
- **Connection Management**: Added keep-alive and cache control headers

### 2. Request Rate Limiting
- **Request Queue**: Implemented internal request queue to control API call frequency
- **Delay Between Requests**: Added 100ms delay between consecutive requests
- **Request Interceptor**: Automatic rate limiting at the axios level

### 3. Retry Logic with Exponential Backoff
- **Automatic Retries**: Retry failed requests with 418/429 status codes
- **Smart Delays**: 2s delay for 418 errors, 5s for 429 errors
- **Single Retry**: Prevents infinite retry loops

### 4. Caching and Fallback Mechanisms
- **Intelligent Caching**: Use cached data when API is unavailable
- **Cache-First Strategy**: Check cache before making API calls
- **Mock Data Fallback**: Provide mock data as last resort to prevent complete failure

### 5. Reduced API Call Frequency
- **Longer Intervals**: Increased price update interval from 5s to 30s
- **Smart Updates**: Skip updates when cache is empty or API is failing
- **Batch Operations**: Use bulk API endpoints instead of individual calls

## Code Changes

### BinanceService.ts
```typescript
// Enhanced axios instance with proper headers and retry logic
private createAxiosInstance(): AxiosInstance {
  const instance = axios.create({
    baseURL: this.baseURL,
    timeout: 30000,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...',
      'Accept': 'application/json',
      'Accept-Language': 'en-US,en;q=0.9',
      // ... other headers
    }
  });

  // Rate limiting interceptor
  instance.interceptors.request.use(async (config) => {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.REQUEST_DELAY) {
      await new Promise(resolve => setTimeout(resolve, this.REQUEST_DELAY - timeSinceLastRequest));
    }
    
    this.lastRequestTime = Date.now();
    return config;
  });

  // Retry logic interceptor
  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      if ((error.response?.status === 418 || error.response?.status === 429) && !config._retry) {
        config._retry = true;
        const delay = error.response?.status === 429 ? 5000 : 2000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return instance(config);
      }
      return Promise.reject(error);
    }
  );
}
```

### CoinListService.ts
```typescript
// Reduced update frequency
private startBackgroundUpdates() {
  this.priceUpdateInterval = setInterval(async () => {
    if (!this.isUpdatingPrices && this.coinListCache.size > 0) {
      await this.updatePricesOnly();
    }
  }, 30000); // Increased from 5000ms to 30000ms
}
```

## Additional Strategies (If Issues Persist)

### 1. Use Alternative Endpoints
- Switch to Binance US API (`api.binance.us`) if available
- Use CoinGecko as primary data source instead of Binance
- Implement multiple data source fallbacks

### 2. Proxy/VPN Solutions
- Use rotating proxy services
- Implement request routing through different IP addresses
- Consider using cloud functions in different regions

### 3. API Key Authentication
- Add Binance API keys to increase rate limits
- Use signed requests for higher quotas
- Implement proper API key rotation

### 4. WebSocket Priority
- Rely more heavily on WebSocket streams for real-time data
- Reduce REST API calls by using cached WebSocket data
- Implement WebSocket reconnection with exponential backoff

## Environment Variables
Add these to your Render environment:

```env
# Optional: Binance API credentials for higher limits
BINANCE_API_KEY=your_api_key
BINANCE_SECRET_KEY=your_secret_key

# Rate limiting configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Monitoring
- Check logs for rate limiting patterns
- Monitor API response times and success rates
- Set up alerts for consecutive API failures

## Testing
Deploy these changes and monitor the logs for:
- Reduced 418 errors
- Successful fallback to cached data
- Proper retry behavior
- Overall system stability
